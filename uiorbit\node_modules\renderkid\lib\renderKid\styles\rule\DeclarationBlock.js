"use strict";

function _typeof(obj) { "@babel/helpers - typeof"; if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

// Generated by CoffeeScript 2.5.1
var Arbitrary, DeclarationBlock, declarationClasses;

module.exports = DeclarationBlock = function () {
  var self;

  var DeclarationBlock = /*#__PURE__*/function () {
    function DeclarationBlock() {
      _classCallCheck(this, DeclarationBlock);

      this._declarations = {};
    }

    _createClass(DeclarationBlock, [{
      key: "set",
      value: function set(prop, value) {
        var key, val;

        if (_typeof(prop) === 'object') {
          for (key in prop) {
            val = prop[key];
            this.set(key, val);
          }

          return this;
        }

        prop = self.sanitizeProp(prop);

        this._getDeclarationClass(prop).setOnto(this._declarations, prop, value);

        return this;
      }
    }, {
      key: "_getDeclarationClass",
      value: function _getDeclarationClass(prop) {
        var cls;

        if (prop[0] === '_') {
          return Arbitrary;
        }

        if (!(cls = declarationClasses[prop])) {
          throw Error("Unknown property `".concat(prop, "`. Write it as `_").concat(prop, "` if you're defining a custom property"));
        }

        return cls;
      }
    }], [{
      key: "sanitizeProp",
      value: function sanitizeProp(prop) {
        return String(prop).trim();
      }
    }]);

    return DeclarationBlock;
  }();

  ;
  self = DeclarationBlock;
  return DeclarationBlock;
}.call(void 0);

Arbitrary = require('./declarationBlock/Arbitrary');
declarationClasses = {
  color: require('./declarationBlock/Color'),
  background: require('./declarationBlock/Background'),
  width: require('./declarationBlock/Width'),
  height: require('./declarationBlock/Height'),
  bullet: require('./declarationBlock/Bullet'),
  display: require('./declarationBlock/Display'),
  margin: require('./declarationBlock/Margin'),
  marginTop: require('./declarationBlock/MarginTop'),
  marginLeft: require('./declarationBlock/MarginLeft'),
  marginRight: require('./declarationBlock/MarginRight'),
  marginBottom: require('./declarationBlock/MarginBottom'),
  padding: require('./declarationBlock/Padding'),
  paddingTop: require('./declarationBlock/PaddingTop'),
  paddingLeft: require('./declarationBlock/PaddingLeft'),
  paddingRight: require('./declarationBlock/PaddingRight'),
  paddingBottom: require('./declarationBlock/PaddingBottom')
};