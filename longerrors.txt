PS D:\Project\New folder\uiorbit> npm run compile

> uiorbit@1.0.0 compile
> webpack

    [webpack-cli] Compiler starting... 
    [webpack-cli] Compiler is using config: 'D:\Project\New folder\uiorbit\webpack.config.js'
    [webpack-cli] Compiler starting... 
    [webpack-cli] Compiler is using config: 'D:\Project\New folder\uiorbit\webpack.config.js'
    [webpack-cli] Compiler finished
    [webpack-cli] Compiler finished
asset extension.js 114 KiB [compared for emit] (name: main) 1 related asset
modules by path ./node_modules/ 66.9 KiB
  modules by path ./node_modules/fs-extra/lib/ 38.5 KiB 27 modules
  modules by path ./node_modules/graceful-fs/*.js 25.4 KiB 4 modules
  modules by path ./node_modules/jsonfile/*.js 2.34 KiB 2 modules
  ./node_modules/universalify/index.js 706 bytes [built] [code generated]
modules by path ./src/ 41.5 KiB
  modules by path ./src/core/*.ts 10.2 KiB 2 modules
  modules by path ./src/services/*.ts 15.9 KiB 2 modules
  + 3 modules
+ 7 modules

ERROR in D:\Project\New folder\uiorbit\src\services\__tests__\CommandService.test.ts
89:63-70
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\__tests__\CommandService.test.ts(89,64) 
      TS7006: Parameter 'options' implicitly has an 'any' type.

ERROR in D:\Project\New folder\uiorbit\src\services\__tests__\CommandService.test.ts
89:72-76
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\__tests__\CommandService.test.ts(89,73) 
      TS7006: Parameter 'task' implicitly has an 'any' type.

ERROR in D:\Project\New folder\uiorbit\src\services\__tests__\CommandService.test.ts
109:52-59
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\__tests__\CommandService.test.ts(109,53)
      TS2345: Argument of type '{ fsPath: string; }' is not assignable to parameter of type 'Uri'.
  Type '{ fsPath: string; }' is missing the following properties from type 'Uri': scheme, authority, path, query, and 3 more.

ERROR in D:\Project\New folder\uiorbit\src\services\__tests__\CommandService.test.ts
164:63-70
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\__tests__\CommandService.test.ts(164,64)
      TS7006: Parameter 'options' implicitly has an 'any' type.

ERROR in D:\Project\New folder\uiorbit\src\services\__tests__\CommandService.test.ts
164:72-76
[tsl] ERROR in D:\Project\New folder\uiorbit\src\services\__tests__\CommandService.test.ts(164,73)
      TS7006: Parameter 'task' implicitly has an 'any' type.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx
2:30-58
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx(2,31)
      TS6142: Module './components/MessageBubble' was resolved to 'D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx', but '--jsx' is not set.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx
3:29-56
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx(3,30)
      TS6142: Module './components/MessageInput' was resolved to 'D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx', but '--jsx' is not set.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx
4:32-62
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx(4,33)
      TS6142: Module './components/TypingIndicator' was resolved to 'D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx', but '--jsx' is not set.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx
5:31-60
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx(5,32)
      TS6142: Module './components/WelcomeMessage' was resolved to 'D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx', but '--jsx' is not set.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx
38:29-35
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx(38,30)
      TS2304: Cannot find name 'window'.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx
41:6-26
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx(41,7)
      TS18047: 'vscodeApiRef.current' is possibly 'null'.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx
70:4-10
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx(70,5)
      TS2304: Cannot find name 'window'.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx
71:17-23
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx(71,18)
      TS2304: Cannot find name 'window'.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx
77:31-40
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx(77,32)
      TS2812: Property 'scrollTop' does not exist on type 'HTMLDivElement'. Try changing the 'lib' compiler option to include 'dom'.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx
77:68-80
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx(77,69)
      TS2812: Property 'scrollHeight' does not exist on type 'HTMLDivElement'. Try changing the 'lib' compiler 
option to include 'dom'.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx
117:4-25
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx(117,5)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx
118:6-61
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx(118,7)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx
119:46-64
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx(119,47)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx
122:10-125:12
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx(122,11)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx
128:21-40
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx(128,22)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx
131:6-135:8
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\App.tsx(131,7)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\index.tsx
3:20-27
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\index.tsx(3,21)
      TS6142: Module './App' was resolved to 'D:\Project\New folder\uiorbit\src\webview\react\App.tsx', but '--jsx' is not set.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\index.tsx
6:18-26
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\index.tsx(6,19)
      TS2584: Cannot find name 'document'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\index.tsx
9:14-21
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\index.tsx(9,15)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\CodeBlock.tsx
13:12-21
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\CodeBlock.tsx(13,13)
      TS2304: Cannot find name 'navigator'.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\CodeBlock.tsx
58:4-32
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\CodeBlock.tsx(58,5)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\CodeBlock.tsx
59:6-35
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\CodeBlock.tsx(59,7)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\CodeBlock.tsx
60:8-41
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\CodeBlock.tsx(60,9)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\CodeBlock.tsx
61:8-65:9
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\CodeBlock.tsx(61,9)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\CodeBlock.tsx
69:6-36
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\CodeBlock.tsx(69,7)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\CodeBlock.tsx
70:8-75:10
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\CodeBlock.tsx(70,9)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx
2:24-32
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx(2,25)
      TS6142: Module '../App' was resolved to 'D:\Project\New folder\uiorbit\src\webview\react\App.tsx', but '--jsx' is not set.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx
3:26-39
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx(3,27)
      TS6142: Module './CodeBlock' was resolved to 'D:\Project\New folder\uiorbit\src\webview\react\components\CodeBlock.tsx', but '--jsx' is not set.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx
67:4-90
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx(67,5)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx
68:6-39
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx(68,7)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx
70:10-27
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx(70,11)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx
72:14-44
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx(72,15)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx
74:14-77:16
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx(74,15)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx
83:6-41
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageBubble.tsx(83,7)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx
20:26-31
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx(20,27)
      TS2812: Property 'style' does not exist on type 'HTMLTextAreaElement'. Try changing the 'lib' compiler option to include 'dom'.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx
21:26-31
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx(21,27)
      TS2812: Property 'style' does not exist on type 'HTMLTextAreaElement'. Try changing the 'lib' compiler option to include 'dom'.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx
21:64-76
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx(21,65)
      TS2812: Property 'scrollHeight' does not exist on type 'HTMLTextAreaElement'. Try changing the 'lib' compiler option to include 'dom'.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx
41:24-29
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx(41,25)
      TS2339: Property 'value' does not exist on type 'EventTarget & HTMLTextAreaElement'.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx
45:4-70
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx(45,5)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx
46:6-37
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx(46,7)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx
47:8-57:10
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx(47,9)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx
58:8-63:9
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx(58,9)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx
64:10-73:11
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx(64,11)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx
74:12-49
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx(74,13)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx
75:12-51
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx(75,13)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx
79:6-36
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx(79,7)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx
80:8-42
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx(80,9)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx
83:8-31
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\MessageInput.tsx(83,9)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx
5:4-38
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx(5,5)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx
6:6-37
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx(6,7)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx
7:8-37
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx(7,9)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx
9:6-38
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx(9,7)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx
10:8-37
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx(10,9)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx
11:8-37
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx(11,9)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx
12:10-32
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx(12,11)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx
13:10-32
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx(13,11)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx
14:10-32
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\TypingIndicator.tsx(14,11)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
5:4-37
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(5,5)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
6:6-36
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(6,7)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
7:8-41
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(7,9)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
8:10-40
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(8,11)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
9:10-38
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(9,11)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
10:12-39
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(10,13)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
14:6-39
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(14,7)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
15:8-12
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(15,9)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
16:8-11
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(16,9)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
17:8-42
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(17,9)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
18:10-35
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(18,11)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
19:12-43
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(19,13)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
20:12-18
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(20,13)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
22:10-35
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(22,11)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
23:12-43
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(23,13)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
24:12-18
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(24,13)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
26:10-35
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(26,11)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
27:12-43
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(27,13)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
28:12-18
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(28,13)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx
31:8-36
[tsl] ERROR in D:\Project\New folder\uiorbit\src\webview\react\components\WelcomeMessage.tsx(31,9)
      TS17004: Cannot use JSX unless the '--jsx' flag is provided.

82 errors have detailed information that is not shown.
Use 'stats.errorDetails: true' resp. '--stats-error-details' to show it.

webpack 5.99.9 compiled with 82 errors in 13556 ms

asset webview.js 1.57 MiB [emitted] (name: main) 1 related asset
runtime modules 1.07 KiB 6 modules
modules by path ./node_modules/ 1.52 MiB
  modules by path ./node_modules/react/ 73.9 KiB 6 modules
  modules by path ./node_modules/react-dom/ 1.42 MiB 6 modules
  modules by path ./node_modules/style-loader/dist/runtime/*.js 5.84 KiB 6 modules
  modules by path ./node_modules/scheduler/ 22 KiB 3 modules
  modules by path ./node_modules/css-loader/dist/runtime/*.js 2.74 KiB 2 modules
modules by path ./src/webview/react/ 26.6 KiB
  modules by path ./src/webview/react/components/*.tsx 10.5 KiB 5 modules
  modules by path ./src/webview/react/*.tsx 3.74 KiB 2 modules
  modules by path ./src/webview/react/styles/*.css 12.4 KiB
    ./src/webview/react/styles/App.css 1.17 KiB [built] [code generated]
    ./node_modules/css-loader/dist/cjs.js!./src/webview/react/styles/App.css 11.2 KiB [built] [code generated] 
webpack 5.99.9 compiled successfully in 13490 ms
PS D:\Project\New folder\uiorbit> 