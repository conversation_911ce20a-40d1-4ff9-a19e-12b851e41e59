# UIOrbit - AI Frontend Developer

**World-class AI assistant for modern UI/UX development with codebase understanding**

UIOrbit is a VS Code extension that brings intelligent AI assistance to frontend development, similar to Augment Code but specialized for UI/UX development. It features automatic codebase indexing, context understanding, and intelligent project detection.

## 🚀 Current Status: Phase 1, Week 2 - React Webview Foundation Complete!

✅ **Phase 1, Week 1 Complete:**
- Extension scaffold with TypeScript
- Service registry architecture
- Configuration service with .env support
- Command registration system
- Logging infrastructure
- Testing framework setup

✅ **Phase 1, Week 2 - Day 1-3 Complete:**
- **React Webview Foundation**: Complete React app within VS Code webview
- **Modern Chat Interface**: Beautiful, responsive chat UI with VS Code theme integration
- **Message Passing Protocol**: Seamless communication between extension and React webview
- **Component Architecture**: Modular React components (MessageBubble, MessageInput, TypingIndicator, etc.)
- **Code Syntax Highlighting**: Basic syntax highlighting with copy functionality
- **Animated UI Elements**: Typing indicators, welcome screen with orbital animation
- **Build System**: Dual webpack configuration for extension and React webview

🔄 **In Progress: Phase 1, Week 2 - Day 4-5:**
- Enhanced code syntax highlighting
- File attachment support
- @ mentions for code references
- Better error handling
- Improved accessibility

## 🎯 Key Features (Planned)

### Phase 1: Core Infrastructure
- ✅ **Chat Interface**: Beautiful React-based chat interface with natural language interaction
- ✅ **Configuration Management**: .env file and VS Code settings integration
- ✅ **React Webview**: Modern React app with component architecture and VS Code theme integration
- 🔄 **File Operations**: Complete CRUD operations for files (Week 3)
- 📅 **AI Integration**: OpenAI API integration with context awareness (Week 4)

### Phase 2: Codebase Intelligence
- 📅 **Automatic Indexing**: Real-time codebase analysis and understanding
- 📅 **Vector Search**: Semantic code search and similarity matching
- 📅 **AST Analysis**: Deep code structure understanding
- 📅 **Context Engine**: Intelligent context selection and aggregation

### Phase 3: UI/UX Intelligence
- 📅 **Design System Analysis**: Automatic design token extraction
- 📅 **Modern Patterns**: 2024-2025 UI/UX trends and best practices
- 📅 **Component Generation**: Intelligent component creation with variants
- 📅 **Accessibility**: WCAG 2.1 AA+ compliance automation

### Phase 4: Agent Mode
- 📅 **Task Planning**: Multi-step task decomposition and execution
- 📅 **Team Collaboration**: Shared components and knowledge base
- 📅 **Testing Integration**: Automated test generation
- 📅 **Performance Optimization**: Bundle analysis and optimization

## 🧠 Intelligent Project Detection

UIOrbit automatically detects your project context and adapts accordingly:

- **No Project**: Auto-creates Vite-React projects for new development
- **Existing Project**: Learns your codebase patterns and follows your conventions
- **Explicit Override**: Allows component-only generation when requested
- **Framework Detection**: Supports React, Vue, Angular, Svelte, and vanilla JS

## 🛠️ Development Setup

### Prerequisites
- VS Code 1.85.0 or higher
- Node.js 20.x or higher
- OpenAI API key (for AI features)

### Installation for Development

1. **Clone and setup:**
   ```bash
   cd uiorbit
   npm install
   ```

2. **Configure environment:**
   - Copy `.env.example` to `.env`
   - Add your OpenAI API key: `OPENAI_API_KEY=your_key_here`

3. **Compile and test:**
   ```bash
   npm run compile
   ```

4. **Run extension:**
   - Press `F5` in VS Code to launch Extension Development Host
   - Or use "Run Extension" from the debug panel

### Testing the Extension

1. **Open the chat interface:**
   - Look for "UIOrbit Chat" in the Explorer panel
   - Or run command: "UIOrbit: Open Chat"

2. **Try basic commands:**
   - Right-click on a folder → "Generate Component"
   - Select code → Right-click → "Analyze Code"

3. **Test React chat interface:**
   - Type messages in the beautiful React chat interface
   - See animated typing indicators and message bubbles
   - Test code syntax highlighting with copy functionality
   - Experience the modern welcome screen with orbital animation
   - Currently shows placeholder responses (AI integration in Week 4)

## ⚙️ Configuration

### VS Code Settings
- `uiorbit.openaiApiKey`: Your OpenAI API key
- `uiorbit.defaultFramework`: Default framework (react, vue, angular, svelte)
- `uiorbit.defaultStyling`: Default styling approach (tailwind, css, scss)
- `uiorbit.enableAccessibility`: Enable accessibility features
- `uiorbit.enableResponsiveDesign`: Enable responsive design
- `uiorbit.debugMode`: Enable debug logging

### Environment Variables (.env)
See `.env.example` for all available configuration options.

## 📋 Development Roadmap

### Week 1: Project Setup & Extension Scaffold ✅
- [x] Development environment setup
- [x] Extension scaffold with TypeScript
- [x] Core architecture implementation
- [x] Testing framework setup

### Week 2: React Webview Chat Interface ✅
- [x] React webview foundation with webpack configuration
- [x] Beautiful chat components & UI with VS Code theme integration
- [x] Message passing protocol between extension and React
- [x] Code syntax highlighting with copy functionality
- [x] Animated typing indicators and welcome screen
- [x] Responsive design and modern CSS animations
- [/] Enhanced features (Day 4-5 in progress)

### Week 3: File System Integration
- [ ] File operations service
- [ ] Workspace analysis engine
- [ ] Component discovery & mapping

### Week 4: Basic AI Integration
- [ ] OpenAI service architecture
- [ ] Context-aware generation
- [ ] Error handling and validation

## 🤝 Contributing

This is currently in active development. The extension follows a structured 16-week development plan with 4 phases.

## 📄 License

MIT License - See LICENSE file for details.

---

**Built with ❤️ for the frontend development community**
