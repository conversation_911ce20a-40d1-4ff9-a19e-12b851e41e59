import React, { useState, useEffect, useRef } from 'react';
import { MessageBubble } from './components/MessageBubble';
import { MessageInput } from './components/MessageInput';
import { TypingIndicator } from './components/TypingIndicator';
import { WelcomeMessage } from './components/WelcomeMessage';
import './styles/App.css';

export interface Message {
  id: string;
  text: string;
  sender: 'user' | 'assistant';
  timestamp: string;
  isError?: boolean;
}

export interface VSCodeAPI {
  postMessage: (message: any) => void;
  getState: () => any;
  setState: (state: any) => void;
}

declare global {
  interface Window {
    acquireVsCodeApi: () => VSCodeAPI;
  }
}

export const App: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const vscodeApiRef = useRef<VSCodeAPI | null>(null);

  // Initialize VS Code API
  useEffect(() => {
    try {
      vscodeApiRef.current = window.acquireVsCodeApi();
      
      // Send ready message to extension
      vscodeApiRef.current.postMessage({ type: 'ready' });
      
      console.log('VS Code API initialized successfully');
    } catch (error) {
      console.error('Failed to initialize VS Code API:', error);
    }
  }, []);

  // Handle messages from VS Code extension
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      console.log('Received message from extension:', message);

      switch (message.type) {
        case 'assistant-message':
          handleAssistantMessage(message);
          break;
        case 'assistant-typing':
          setIsTyping(message.isTyping);
          break;
        case 'welcome':
          setIsReady(true);
          break;
        default:
          console.warn('Unknown message type:', message.type);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages, isTyping]);

  const handleAssistantMessage = (messageData: any) => {
    const newMessage: Message = {
      id: `assistant-${Date.now()}`,
      text: messageData.text,
      sender: 'assistant',
      timestamp: messageData.timestamp || new Date().toISOString(),
      isError: messageData.isError || false
    };

    setMessages(prev => [...prev, newMessage]);
    setIsTyping(false);
  };

  const handleSendMessage = (text: string) => {
    if (!text.trim() || isTyping || !vscodeApiRef.current) {
      return;
    }

    // Add user message to chat
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      text: text.trim(),
      sender: 'user',
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);

    // Send message to extension
    vscodeApiRef.current.postMessage({
      type: 'chat-message',
      text: text.trim()
    });
  };

  return (
    <div className="app">
      <div className="chat-container" ref={chatContainerRef}>
        {!isReady && messages.length === 0 && <WelcomeMessage />}
        
        {messages.map((message) => (
          <MessageBubble
            key={message.id}
            message={message}
          />
        ))}
        
        {isTyping && <TypingIndicator />}
      </div>

      <MessageInput
        onSendMessage={handleSendMessage}
        disabled={isTyping}
        placeholder="Ask me anything about frontend development..."
      />
    </div>
  );
};
