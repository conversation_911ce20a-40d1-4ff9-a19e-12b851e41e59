import React from 'react';

export const App: React.FC = () => {
  console.log('React App component rendering...');

  return (
    <div style={{
      padding: '20px',
      fontFamily: 'var(--vscode-font-family)',
      color: 'var(--vscode-foreground)',
      backgroundColor: 'var(--vscode-editor-background)',
      height: '100vh'
    }}>
      <h1>🚀 UIOrbit Chat - React Version</h1>
      <p>React is working! This is the React-powered chat interface.</p>
      <div style={{
        padding: '10px',
        backgroundColor: 'var(--vscode-input-background)',
        border: '1px solid var(--vscode-input-border)',
        borderRadius: '4px',
        marginTop: '20px'
      }}>
        <strong>Status:</strong> React components loaded successfully!
      </div>
    </div>
  );
};
