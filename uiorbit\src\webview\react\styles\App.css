/* Main App Styles */
.app {
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
  color: var(--vscode-foreground);
  background-color: var(--vscode-editor-background);
  height: 100vh;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  overflow-y: auto;
  scroll-behavior: smooth;
}

/* Message Bubble Styles */
.message-bubble {
  max-width: 85%;
  padding: 12px 16px;
  border-radius: 12px;
  word-wrap: break-word;
  position: relative;
  animation: slideIn 0.3s ease-out;
}

.user-message {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  align-self: flex-end;
  border-bottom-right-radius: 4px;
}

.assistant-message {
  background-color: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  align-self: flex-start;
  border-bottom-left-radius: 4px;
}

.error-message {
  background-color: var(--vscode-errorBackground);
  border: 1px solid var(--vscode-errorBorder);
  color: var(--vscode-errorForeground);
}

.message-content {
  margin-bottom: 4px;
}

.message-text {
  line-height: 1.5;
  white-space: pre-wrap;
}

.message-timestamp {
  font-size: 0.75em;
  opacity: 0.6;
  text-align: right;
  margin-top: 4px;
}

/* Code Block Styles */
.code-block {
  margin: 8px 0;
  border-radius: 6px;
  overflow: hidden;
  background-color: var(--vscode-textCodeBlock-background);
  border: 1px solid var(--vscode-widget-border);
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--vscode-editorGroupHeader-tabsBackground);
  border-bottom: 1px solid var(--vscode-widget-border);
}

.language-label {
  font-size: 0.8em;
  color: var(--vscode-descriptionForeground);
  font-weight: 500;
}

.copy-button {
  background: none;
  border: none;
  color: var(--vscode-foreground);
  cursor: pointer;
  padding: 4px;
  border-radius: 3px;
  font-size: 0.9em;
  transition: background-color 0.2s;
}

.copy-button:hover {
  background-color: var(--vscode-toolbar-hoverBackground);
}

.code-content {
  padding: 12px;
  margin: 0;
  overflow-x: auto;
  font-family: var(--vscode-editor-font-family);
  font-size: var(--vscode-editor-font-size);
  line-height: 1.4;
}

.code-content code {
  color: var(--vscode-editor-foreground);
}

/* Syntax highlighting */
.keyword {
  color: var(--vscode-symbolIcon-keywordForeground, #569cd6);
  font-weight: bold;
}

.string {
  color: var(--vscode-symbolIcon-stringForeground, #ce9178);
}

.comment {
  color: var(--vscode-symbolIcon-colorForeground, #6a9955);
  font-style: italic;
}

/* Message Input Styles */
.message-input-container {
  padding: 16px;
  border-top: 1px solid var(--vscode-widget-border);
  background-color: var(--vscode-editor-background);
}

.input-wrapper {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.message-textarea {
  flex: 1;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  border: 1px solid var(--vscode-input-border);
  border-radius: 8px;
  padding: 12px;
  font-family: inherit;
  font-size: inherit;
  resize: none;
  min-height: 20px;
  max-height: 120px;
  line-height: 1.4;
  transition: border-color 0.2s;
}

.message-textarea:focus {
  outline: none;
  border-color: var(--vscode-focusBorder);
}

.message-textarea:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-button {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: none;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  min-width: 44px;
  height: 44px;
}

.send-button:hover:not(:disabled) {
  background-color: var(--vscode-button-hoverBackground);
}

.send-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 0.8em;
  color: var(--vscode-descriptionForeground);
}

.character-count {
  font-weight: 500;
}

.hint {
  font-style: italic;
}

/* Typing Indicator Styles */
.typing-indicator {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 16px;
  max-width: 85%;
  align-self: flex-start;
  animation: slideIn 0.3s ease-out;
}

.typing-avatar {
  flex-shrink: 0;
}

.avatar-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--vscode-button-background);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.typing-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.typing-text {
  font-style: italic;
  color: var(--vscode-descriptionForeground);
  font-size: 0.9em;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--vscode-descriptionForeground);
  animation: typing 1.4s infinite ease-in-out;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Welcome Message Styles */
.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40px 20px;
  max-width: 500px;
  margin: 0 auto;
  animation: fadeIn 0.8s ease-out;
}

.welcome-icon {
  margin-bottom: 24px;
}

.orbit-animation {
  position: relative;
  width: 80px;
  height: 80px;
}

.orbit-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 32px;
  animation: pulse 2s infinite;
}

.orbit-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid var(--vscode-button-background);
  border-radius: 50%;
  opacity: 0.3;
  animation: rotate 3s linear infinite;
}

.orbit-dot {
  position: absolute;
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 8px;
  height: 8px;
  background-color: var(--vscode-button-background);
  border-radius: 50%;
}

.welcome-content h2 {
  margin: 0 0 8px 0;
  font-size: 1.5em;
  font-weight: 600;
  color: var(--vscode-foreground);
}

.welcome-content p {
  margin: 0 0 24px 0;
  color: var(--vscode-descriptionForeground);
  font-size: 1em;
}

.welcome-features {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.feature {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9em;
  color: var(--vscode-foreground);
}

.feature-icon {
  font-size: 1.2em;
}

.welcome-hint {
  font-style: italic;
  color: var(--vscode-descriptionForeground);
  font-size: 0.9em;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
